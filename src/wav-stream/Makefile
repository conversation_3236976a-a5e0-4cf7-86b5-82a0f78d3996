# Makefile for WAVIQStream Tests
# BladeRF Video Decoding Project - WAV Stream Module

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g -pedantic
INCLUDES = -I. -I./tests -I../

# Source directories
TEST_DIR = tests
SRC_DIR = .

# Source files for test executable
TEST_SOURCES = $(TEST_DIR)/test_wav_stream.cpp \
               $(TEST_DIR)/comprehensive_tests.cpp \
               $(TEST_DIR)/test_looping.cpp \
               $(TEST_DIR)/performance_tests.cpp \
               $(TEST_DIR)/test_runner.cpp

# Implementation source files
IMPL_SOURCES = $(SRC_DIR)/wav_stream.cpp

# Object files
TEST_OBJECTS = $(TEST_SOURCES:.cpp=.o)
IMPL_OBJECTS = $(IMPL_SOURCES:.cpp=.o)

# Test executable
TEST_EXECUTABLE = wav_stream_tests

# Default target - build and run tests
all: test

# Build the test executable
$(TEST_EXECUTABLE): $(TEST_OBJECTS) $(IMPL_OBJECTS)
	$(CXX) $(CXXFLAGS) -o $@ $^

# Object file compilation
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Build and run tests in one command
test: $(TEST_EXECUTABLE)
	@echo "Running all WAVIQStream tests..."
	@echo "==============================="
	./$(TEST_EXECUTABLE)

# Build only (without running)
build: $(TEST_EXECUTABLE)
	@echo "WAVIQStream test executable built: $(TEST_EXECUTABLE)"

# Quick verification (build and run with limited output)
verify: $(TEST_EXECUTABLE)
	@echo "Quick verification of WAVIQStream..."
	@echo "===================================="
	@./$(TEST_EXECUTABLE) | grep -E "(PASS|FAIL|===|🎉|❌)" | head -15
	@echo "===================================="
	@echo "Run 'make test' for full output"

# Performance test (run only performance benchmarks)
perf: $(TEST_EXECUTABLE)
	@echo "Running WAVIQStream performance tests..."
	@echo "========================================"
	@./$(TEST_EXECUTABLE) | grep -A 20 "PERFORMANCE BENCHMARK"

# Looping test (run only looping functionality tests)
loop: $(TEST_EXECUTABLE)
	@echo "Running WAVIQStream looping tests..."
	@echo "===================================="
	@./$(TEST_EXECUTABLE) | grep -A 30 "Looping Tests"

# Clean build artifacts
clean:
	rm -f $(TEST_OBJECTS) $(IMPL_OBJECTS)
	rm -f $(TEST_EXECUTABLE)
	rm -f *.o tests/*.o
	rm -f tests/test_*.wav

# Clean everything
distclean: clean

# Install dependencies (none needed)
deps:
	@echo "No external dependencies required for WAVIQStream tests"

# Validate implementation
validate:
	@echo "Validating WAVIQStream implementation..."
	@echo "======================================="
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only wav_stream.h wav_stream.cpp
	@echo "✓ Implementation syntax validation passed"

# Code analysis
analyze: $(TEST_EXECUTABLE)
	@echo "Running static analysis on WAVIQStream..."
	@echo "========================================"
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -Wall -Wextra -Wpedantic -fsyntax-only wav_stream.h wav_stream.cpp
	@echo "✓ Static analysis completed"

# Memory check (if valgrind is available)
memcheck: $(TEST_EXECUTABLE)
	@echo "Running memory check on WAVIQStream..."
	@echo "====================================="
	@if command -v valgrind >/dev/null 2>&1; then \
		valgrind --leak-check=full --show-leak-kinds=all ./$(TEST_EXECUTABLE); \
	else \
		echo "Valgrind not available - skipping memory check"; \
		echo "Install valgrind for detailed memory analysis"; \
	fi

# Help target
help:
	@echo "WAVIQStream Test Makefile"
	@echo "========================="
	@echo "Available targets:"
	@echo "  test           - Build and run all tests (default)"
	@echo "  build          - Build test executable only"
	@echo "  verify         - Quick verification with limited output"
	@echo "  perf           - Run performance benchmarks only"
	@echo "  loop           - Run looping functionality tests only"
	@echo "  validate       - Validate implementation syntax"
	@echo "  analyze        - Run static code analysis"
	@echo "  memcheck       - Run memory leak detection (requires valgrind)"
	@echo "  clean          - Remove build artifacts"
	@echo "  deps           - Install dependencies (none needed)"
	@echo "  help           - Show this help message"
	@echo ""
	@echo "Simple usage:"
	@echo "  make test      # Build and run all tests"
	@echo "  make verify    # Quick verification"
	@echo "  make clean     # Clean up"
	@echo ""
	@echo "WAVIQStream Features:"
	@echo "  - WAV file parsing and validation"
	@echo "  - 16-bit and 32-bit sample support"
	@echo "  - IQ sample packing (0xQQQQIIII format)"
	@echo "  - Error handling and reporting"
	@echo "  - IIQStream interface compliance"

# Phony targets
.PHONY: all test build verify perf loop clean distclean deps validate analyze memcheck help

# Dependency tracking
$(TEST_DIR)/test_wav_stream.o: $(TEST_DIR)/test_wav_stream.cpp wav_stream.h ../iiq-stream/iiq_stream.h ../types.h

$(TEST_DIR)/comprehensive_tests.o: $(TEST_DIR)/comprehensive_tests.cpp wav_stream.h ../iiq-stream/iiq_stream.h ../types.h

$(TEST_DIR)/test_runner.o: $(TEST_DIR)/test_runner.cpp

$(SRC_DIR)/wav_stream.o: $(SRC_DIR)/wav_stream.cpp wav_stream.h ../iiq-stream/iiq_stream.h ../types.h

# Additional compiler flags for different build types
debug: CXXFLAGS += -DDEBUG -O0 -g3
debug: $(TEST_EXECUTABLE)

release: CXXFLAGS += -DNDEBUG -O3 -flto
release: $(TEST_EXECUTABLE)
