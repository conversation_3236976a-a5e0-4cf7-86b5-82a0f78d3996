#include "wav_stream.h"
#include <cstring>
#include <algorithm>

WAVIQStream::WAVIQStream(const std::string& filename, bool enableLoop)
    : filename_(filename)
    , sourceName_("wav")
    , sampleRate_(0)
    , bitsPerSample_(0)
    , numChannels_(0)
    , dataStartPos_(0)
    , dataSize_(0)
    , currentPos_(0)
    , isActive_(false)
    , isOpen_(false)
    , loopEnabled_(enableLoop)
    , totalSamplesRead_(0) {
}

WAVIQStream::~WAVIQStream() {
    close();
}

bool WAVIQStream::open() {
    if (isOpen_) {
        close();
    }

    file_.open(filename_, std::ios::binary);
    if (!file_.is_open()) {
        return setError("Failed to open file: " + filename_);
    }

    if (!parseHeader()) {
        close();
        return false;
    }

    isOpen_ = true;
    isActive_ = true;
    currentPos_ = 0;
    totalSamplesRead_ = 0;
    lastError_.clear();

    return true;
}

bool WAVIQStream::parseHeader() {
    WAVHeader header;
    
    // Read the header
    file_.read(reinterpret_cast<char*>(&header), sizeof(WAVHeader));
    if (file_.gcount() != sizeof(WAVHeader)) {
        return setError("Failed to read WAV header");
    }

    // Validate RIFF signature
    if (std::memcmp(header.riff, "RIFF", 4) != 0) {
        return setError("Invalid RIFF signature");
    }

    // Validate WAVE signature
    if (std::memcmp(header.wave, "WAVE", 4) != 0) {
        return setError("Invalid WAVE signature");
    }

    // Validate format chunk
    if (std::memcmp(header.fmt, "fmt ", 4) != 0) {
        return setError("Invalid format chunk");
    }

    // Validate PCM format
    if (header.audioFormat != 1) {
        return setError("Only PCM format is supported");
    }

    // Validate channel count (must be 2 for I/Q)
    if (header.numChannels != 2) {
        return setError("WAV file must have exactly 2 channels (I and Q)");
    }

    // Validate bits per sample
    if (header.bitsPerSample != 16 && header.bitsPerSample != 32) {
        return setError("Only 16-bit and 32-bit samples are supported");
    }

    // Validate data chunk
    if (std::memcmp(header.data, "data", 4) != 0) {
        return setError("Invalid data chunk");
    }

    // Store parsed values
    sampleRate_ = header.sampleRate;
    bitsPerSample_ = header.bitsPerSample;
    numChannels_ = header.numChannels;
    dataSize_ = header.dataSize;
    dataStartPos_ = file_.tellg();

    // Validate data size alignment
    size_t bytesPerSample = (bitsPerSample_ / 8) * numChannels_;
    if (dataSize_ % bytesPerSample != 0) {
        return setError("Data size is not aligned to sample boundary");
    }

    return true;
}

bool WAVIQStream::readSamples(SampleType* dst, size_t sampleCount) {
    if (!isActive_ || !isOpen_) {
        return false;
    }

    if (sampleCount == 0) {
        return true;
    }

    size_t bytesPerSample = (bitsPerSample_ / 8) * numChannels_;
    size_t totalSamples = dataSize_ / bytesPerSample;
    size_t samplesRead = 0;

    while (samplesRead < sampleCount) {
        // Check if we've reached end of stream
        if (currentPos_ >= totalSamples) {
            if (loopEnabled_) {
                // Reset to beginning for looping
                if (!reset()) {
                    return false;
                }
            } else {
                // No looping, mark as inactive and return
                isActive_ = false;
                return samplesRead > 0; // Return true if we read some samples
            }
        }

        // Calculate how many samples we can read in this iteration
        size_t remainingSamples = sampleCount - samplesRead;
        size_t availableSamples = totalSamples - currentPos_;
        size_t samplesToRead = std::min(remainingSamples, availableSamples);
        size_t bytesToRead = samplesToRead * bytesPerSample;

        if (bitsPerSample_ == 16) {
            // Read 16-bit samples
            std::vector<int16_t> buffer(samplesToRead * 2); // I and Q
            file_.read(reinterpret_cast<char*>(buffer.data()), bytesToRead);

            if (file_.gcount() != static_cast<std::streamsize>(bytesToRead)) {
                isActive_ = false;
                return setError("Failed to read expected number of bytes");
            }

            // Convert to SampleType format
            for (size_t i = 0; i < samplesToRead; ++i) {
                int16_t iSample = buffer[i * 2];     // I component
                int16_t qSample = buffer[i * 2 + 1]; // Q component
                dst[samplesRead + i] = packSample16(iSample, qSample);
            }
        } else { // 32-bit
            // Read 32-bit samples
            std::vector<int32_t> buffer(samplesToRead * 2); // I and Q
            file_.read(reinterpret_cast<char*>(buffer.data()), bytesToRead);

            if (file_.gcount() != static_cast<std::streamsize>(bytesToRead)) {
                isActive_ = false;
                return setError("Failed to read expected number of bytes");
            }

            // Convert to SampleType format
            for (size_t i = 0; i < samplesToRead; ++i) {
                int32_t iSample = buffer[i * 2];     // I component
                int32_t qSample = buffer[i * 2 + 1]; // Q component
                dst[samplesRead + i] = packSample32(iSample, qSample);
            }
        }

        currentPos_ += samplesToRead;
        samplesRead += samplesToRead;
        totalSamplesRead_ += samplesToRead;
    }

    return true;
}

SampleRateType WAVIQStream::sampleRate() const noexcept {
    return sampleRate_;
}

const std::string& WAVIQStream::sourceName() const noexcept {
    return sourceName_;
}

bool WAVIQStream::isActive() const noexcept {
    return isActive_ && isOpen_;
}

void WAVIQStream::close() noexcept {
    if (file_.is_open()) {
        file_.close();
    }
    isOpen_ = false;
    isActive_ = false;
}

const std::string& WAVIQStream::lastError() const noexcept {
    return lastError_;
}

bool WAVIQStream::setError(const std::string& error) {
    lastError_ = error;
    isActive_ = false;
    return false;
}

void WAVIQStream::setLooping(bool enable) noexcept {
    loopEnabled_ = enable;
}

bool WAVIQStream::isLooping() const noexcept {
    return loopEnabled_;
}

uint64_t WAVIQStream::getTotalSamplesRead() const noexcept {
    return totalSamplesRead_;
}

bool WAVIQStream::reset() {
    if (!isOpen_) {
        return setError("Cannot reset: stream is not open");
    }

    // Seek back to start of data
    file_.seekg(dataStartPos_, std::ios::beg);
    if (file_.fail()) {
        return setError("Failed to seek to start of data");
    }

    currentPos_ = 0;
    isActive_ = true;
    lastError_.clear();

    return true;
}