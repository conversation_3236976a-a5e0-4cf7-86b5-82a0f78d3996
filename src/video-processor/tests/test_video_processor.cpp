#include <iostream>
#include <cassert>
#include <stdexcept>
#include "../video_processor.h"

namespace VideoProcessorTests {

void test_construction() {
    std::cout << "Testing VideoProcessor construction..." << std::endl;
    
    IQVideoProcessor::VideoProcessor processor;
    
    // Should be constructed successfully
    std::cout << "✓ VideoProcessor constructed successfully" << std::endl;
}

void test_initialization() {
    std::cout << "Testing VideoProcessor initialization..." << std::endl;
    
    IQVideoProcessor::VideoProcessor processor;
    
    // Test initial state
    bool result = processor.initialize();
    assert(result == true);
    std::cout << "✓ VideoProcessor initialization successful" << std::endl;
    
    // Test double initialization (should be idempotent)
    result = processor.initialize();
    assert(result == true);
    std::cout << "✓ Double initialization handled correctly" << std::endl;
}

void test_shutdown() {
    std::cout << "Testing VideoProcessor shutdown..." << std::endl;
    
    IQVideoProcessor::VideoProcessor processor;
    
    // Initialize first
    processor.initialize();
    
    // Test shutdown
    processor.shutdown();
    std::cout << "✓ VideoProcessor shutdown successful" << std::endl;
    
    // Test double shutdown (should be idempotent)
    processor.shutdown();
    std::cout << "✓ Double shutdown handled correctly" << std::endl;
}

void test_lifecycle() {
    std::cout << "Testing VideoProcessor lifecycle..." << std::endl;
    
    IQVideoProcessor::VideoProcessor processor;
    
    // Test multiple init/shutdown cycles
    for (int i = 0; i < 3; ++i) {
        bool result = processor.initialize();
        assert(result == true);
        processor.shutdown();
    }
    
    std::cout << "✓ Multiple lifecycle cycles handled correctly" << std::endl;
}

void test_destructor() {
    std::cout << "Testing VideoProcessor destructor..." << std::endl;
    
    {
        IQVideoProcessor::VideoProcessor processor;
        processor.initialize();
        // Destructor should be called automatically
    }
    
    std::cout << "✓ VideoProcessor destructor handled correctly" << std::endl;
}

void test_exception_safety() {
    std::cout << "Testing VideoProcessor exception safety..." << std::endl;
    
    try {
        IQVideoProcessor::VideoProcessor processor;
        processor.initialize();
        processor.shutdown();
        
        std::cout << "✓ No exceptions thrown during normal operation" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "❌ Unexpected exception: " << e.what() << std::endl;
        throw;
    }
}

} // namespace VideoProcessorTests

// Main test runner function
int run_video_processor_tests() {
    std::cout << "\n🧪 Running VideoProcessor Core Tests" << std::endl;
    std::cout << "====================================" << std::endl;

    try {
        VideoProcessorTests::test_construction();
        VideoProcessorTests::test_initialization();
        VideoProcessorTests::test_shutdown();
        VideoProcessorTests::test_lifecycle();
        VideoProcessorTests::test_destructor();
        VideoProcessorTests::test_exception_safety();

        std::cout << "\n🎉 All VideoProcessor core tests PASSED!" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "\n❌ VideoProcessor test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "\n❌ VideoProcessor test failed with unknown exception" << std::endl;
        return 1;
    }
}
