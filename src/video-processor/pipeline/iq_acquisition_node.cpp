#include "iq_acquisition_node.h"

namespace IQVideoProcessor::Pipeline {

#define NUM_OF_WRITE_CHUNKS 16

IQAcquisitionNode::IQAcquisitionNode(std::unique_ptr<IIQStream> stream, size_t iqStreamReadSize, size_t iqOutputChunkSize, size_t iqOutputChunkOverlapSize)
  : iqStreamReadSize_(iqStreamReadSize), iqOutputChunkSize_(iqOutputChunkSize),
    iqOutputChunkOverlapSize_(iqOutputChunkOverlapSize), iqStream_(std::move(stream)), samplesProcessed_(0) {

  if (!iqStream_) return; // just do nothing if stream is null

  assert(iqStreamReadSize_ > 0);
  assert(iqOutputChunkSize_ > 0);
  assert(iqOutputChunkOverlapSize_ > 0);


  // Initialize the reusable chunk buffer with sufficient capacity
  currentChunk_.chunkBuffer.reserve(iqOutputChunkSize_);

  /*
  * Creating a chunk processor that generates chunks with the pre-calculated sizes
  * passed from VideoProcessor.
  */
  chunkProcessor_ = std::make_unique<ChunkProcessor<SampleType>>(
    iqStreamReadSize_,
    iqOutputChunkSize_,
    iqOutputChunkOverlapSize_,
    NUM_OF_WRITE_CHUNKS,
    ([this](const SampleType *buffer, const size_t chunkSize, const size_t overlapSize) {
      this->onOutputChunkReady(buffer, chunkSize, overlapSize);
    })
  );
  // Making the node running
  setRunning();
}

IQAcquisitionNode::~IQAcquisitionNode() {
  PipelineComponent::stop();
}

bool IQAcquisitionNode::process(bool &&input) {
  return false; // We don't process input directly in this node
}

bool IQAcquisitionNode::hasPendingWork() const {
  return false;
}

bool IQAcquisitionNode::tick() {
  if (!running()) {
    return false; // Node is not running
  }

  auto *buffer = chunkProcessor_->getWriteChunkPtr();
  if (!iqStream_->readSamples(buffer, iqStreamReadSize_)) {
    stop();
    return false;
  }

  if (!running()) {
    return false; // Node is not running
  }

  chunkProcessor_->commitWriteChunk();
  return true;
}

void IQAcquisitionNode::onOutputChunkReady(const SampleType *chunkBuffer, const size_t chunkSize, const size_t overlapSize) {
  if (!running()) return;

  // Reuse the currentChunk_ member variable instead of creating a new instance
  currentChunk_.chunkBuffer.clear();
  currentChunk_.chunkBuffer.assign(chunkBuffer, chunkBuffer + chunkSize);
  currentChunk_.chunkSize = chunkSize;
  currentChunk_.overlapSize = overlapSize;

  if (this->sendOutput(&currentChunk_)) {
    samplesProcessed_ += chunkSize - overlapSize; // Update processed samples count
  } else {
    // If output link rejected the data, we should stop processing
    stop();
  }
}

} // namespace IQVideoProcessor::Pipeline

