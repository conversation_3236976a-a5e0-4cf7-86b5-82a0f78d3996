#pragma once

#include "iq_acquisition_node_types.h"
#include "../../stream-pipeline/async_bridge.h"
#include <memory>

namespace IQVideoProcessor::Pipeline {

/**
 * IQAsyncBridge - Specialized asynchronous bridge for IQ data chunks
 *
 * This bridge provides thread-safe buffering specifically for RawOverlappedIQChunk data
 * between the IQ acquisition stage and downstream processing components. It serves as
 * the connection point between iq_acquisition_node and future processing stages.
 *
 * The bridge handles the asynchronous transfer of IQ data chunks with their overlap
 * information, ensuring proper data flow in the video processing pipeline.
 *
 * Since AsyncBridge is final, this class wraps it and provides IQ-specific functionality.
 */
class IQAsyncBridge {
private:
    std::unique_ptr<SPipeline::AsyncBridge<RawOverlappedIQChunk>> bridge_;
    mutable size_t totalSamplesBuffered_;
    mutable std::mutex sampleCountMutex_;

public:
    /**
     * Constructor
     * @param maxBufferSize Maximum number of IQ chunks to buffer (default: 50)
     * @param tickTimeout Timeout for tick() method blocking (default: 100ms)
     */
    explicit IQAsyncBridge(size_t maxBufferSize = 50,
                          std::chrono::milliseconds tickTimeout = std::chrono::milliseconds(100));

    /**
     * Destructor
     */
    ~IQAsyncBridge() = default;

    // Forward the main AsyncBridge interface
    bool forward(RawOverlappedIQChunk&& data);
    void onOutput(typename SPipeline::IStreamLinkOutput<RawOverlappedIQChunk>::OutputCallback callback);
    bool tick();
    bool hasPendingWork() const;
    void stop();
    bool running() const;
    size_t size() const;

    /**
     * Get the total number of samples currently buffered across all chunks
     * @return Total sample count in buffer
     */
    size_t getTotalBufferedSamples() const;

    /**
     * Get statistics about the current buffer state
     * @return Pair of (chunk_count, total_samples)
     */
    std::pair<size_t, size_t> getBufferStats() const;
};

/**
 * Type alias for direct use of AsyncBridge with RawOverlappedIQChunk
 * Use this when you don't need the additional IQ-specific functionality
 */
using RawIQAsyncBridge = SPipeline::AsyncBridge<RawOverlappedIQChunk>;

} // namespace IQVideoProcessor::Pipeline
