#include "iq_async_bridge.h"

namespace IQVideoProcessor::Pipeline {

IQAsyncBridge::IQAsyncBridge(size_t maxBufferSize, std::chrono::milliseconds tickTimeout)
    : bridge_(std::make_unique<SPipeline::AsyncBridge<RawOverlappedIQChunk>>(maxBufferSize, tickTimeout)),
      totalSamplesBuffered_(0) {
}

bool IQAsyncBridge::forward(RawOverlappedIQChunk&& data) {
    // Store chunk size before moving the data
    size_t chunkSize = data.chunkSize;

    bool result = bridge_->forward(std::move(data));

    // Track sample count only if forward succeeded
    if (result) {
        std::lock_guard<std::mutex> lock(sampleCountMutex_);
        totalSamplesBuffered_ += chunkSize;
    }

    return result;
}

void IQAsyncBridge::onOutput(typename SPipeline::IStreamLinkOutput<RawOverlappedIQChunk>::OutputCallback callback) {
    // Wrap the callback to track sample count
    auto wrappedCallback = [this, callback](RawOverlappedIQChunk&& data) -> bool {
        // Store chunk size before moving the data
        size_t chunkSize = data.chunkSize;

        // Call the original callback
        bool result = callback(std::move(data));

        // Subtract samples when they're consumed (regardless of callback result)
        {
            std::lock_guard<std::mutex> lock(sampleCountMutex_);
            totalSamplesBuffered_ -= chunkSize;
        }

        return result;
    };

    bridge_->onOutput(wrappedCallback);
}

bool IQAsyncBridge::tick() {
    return bridge_->tick();
}

bool IQAsyncBridge::hasPendingWork() const {
    return bridge_->hasPendingWork();
}

void IQAsyncBridge::stop() {
    bridge_->stop();
}

bool IQAsyncBridge::running() const {
    return bridge_->running();
}

size_t IQAsyncBridge::size() const {
    return bridge_->size();
}

size_t IQAsyncBridge::getTotalBufferedSamples() const {
    std::lock_guard<std::mutex> lock(sampleCountMutex_);
    return totalSamplesBuffered_;
}

std::pair<size_t, size_t> IQAsyncBridge::getBufferStats() const {
    std::lock_guard<std::mutex> lock(sampleCountMutex_);
    return {bridge_->size(), totalSamplesBuffered_};
}

} // namespace IQVideoProcessor::Pipeline
