#include "video_processor.h"
#include <cmath>
#include "../types.h"
#include "../configs.h"

namespace IQVideoProcessor {

/**
 * HELPER FUNCTIONS
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 */

// Constants moved from IQAcquisitionNode
constexpr double MIN_LINE_RATE_HZ = 15000.0;           // Minimum line rate for video processing (auto)
constexpr double LINES_PER_CHUNK = 5.0;                // Put 5 lines per chunk for processing

size_t calcOutputChunkSize(const SampleRateType sampleRate) {
    const auto samplesPerVideoLine = static_cast<double>(sampleRate) / MIN_LINE_RATE_HZ;
    return static_cast<size_t>(samplesPerVideoLine * LINES_PER_CHUNK);
}

size_t calcOutputOverlapSize(const SampleRateType sampleRate) {
    constexpr auto ratio = COMPOSITE_SIGNAL_SYNC_SEEK_FILTER_HZ / 2 + 1; // Should be more than 1/2 of the filter frequency
    const auto minimalOverlap = std::lround(sampleRate / ratio);
    return static_cast<size_t>(minimalOverlap) + 1; // Add 1 to ensure we have at least one sample overlap
}

/**
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 */

VideoProcessor::VideoProcessor() : initialized_(false) {
}

VideoProcessor::~VideoProcessor() {
    shutdown();
}

bool VideoProcessor::initialize() {
    if (initialized_) {
        return true;
    }
    
    initialized_ = true;
    return true;
}

void VideoProcessor::shutdown() {
    if (!initialized_) {
        return;
    }
    
    initialized_ = false;
}

} // namespace IQVideoProcessor
